<template>
	<view class="new-users merchant-center" :style="{height:pageHeight}">
		<view class="top" :style="colorStyle">
			<!-- #ifdef MP || APP-PLUS -->
			<view class="sys-head">
				<view class="sys-bar" :style="{height:sysHeight}"></view>
				<!-- #ifdef MP -->
				<view class="sys-title">商家中心</view>
				<!-- #endif -->
				<view class="bg"></view>
			</view>
			<!-- #endif -->
		</view>

		<view class="mid" style="flex:1;overflow: hidden;" :style="colorStyle">
			<scroll-view scroll-y="true" style="height: 100%;">
				<view class="head">
					<view class="user-card merchant-card">
						<view class="bg"></view>
						<view class="user-info">
							<view>
								<view class="avatar-box">
									<image class="avatar" :src="merchantInfo.door_header_img || '/static/images/default-merchant.png'" mode="aspectFill"></image>
								</view>
							</view>

							<view class="info">
								<view class="name">
									<text class="line1 nickname">{{ merchantInfo.store_name || '商家名称' }}</text>
									<!-- 商家/会员模式切换按钮 -->
									<view class="mode-switch" @click="toggleMode">
										<view class="switch-container merchant-mode">
											<view class="switch-icon">
												<text class="iconfont icon-yonghu"></text>
											</view>
											<view class="switch-text">
												切换会员
											</view>
										</view>
									</view>
								</view>
								<view class="num">
									<view class="num-txt status-text" :class="merchantInfo.status === 1 ? 'active' : 'inactive'">
										{{ merchantInfo.status === 1 ? '营业中' : '休息中' }}
									</view>
								</view>
							</view>

							<view class="message" @click="goSettings">
								<view class="iconfont icon-shezhi"></view>
							</view>
						</view>

						<view class="num-wrapper">
							<view class="num-item" @click="goFinance">
								<text class="num">{{ merchantInfo.balance || '0.00' }}</text>
								<view class="txt">账户余额</view>
							</view>
							<view class="num-item" @click="goFinance">
								<text class="num">{{ merchantInfo.todayRevenue || '0.00' }}</text>
								<view class="txt">今日营业额</view>
							</view>
							<view class="num-item" @click="goFinance">
								<text class="num">{{ merchantInfo.monthRevenue || '0.00' }}</text>
								<view class="txt">本月营业额</view>
							</view>
						</view>
					</view>

					<!-- 快捷操作区域 - 参考会员中心的订单中心样式 -->
					<view class="order-wrapper">
						<view class="order-hd flex">
							<view class="left">快捷操作</view>
						</view>
						<view class="order-bd">
							<view class="order-item" @click="goGoodsList">
								<view class="pic">
									<text class="iconfont icon-shangpinguanli"></text>
								</view>
								<view class="txt">商品管理</view>
							</view>
							<view class="order-item" @click="goAddGoods">
								<view class="pic">
									<text class="iconfont icon-tianjiashangpin"></text>
								</view>
								<view class="txt">添加商品</view>
							</view>
							<view class="order-item" @click="goOrderList">
								<view class="pic">
									<text class="iconfont icon-dingdanguanli"></text>
								</view>
								<view class="txt">订单管理</view>
							</view>
							<view class="order-item" @click="goStatistics">
								<view class="pic">
									<text class="iconfont icon-shujutongji"></text>
								</view>
								<view class="txt">数据统计</view>
							</view>
							<view class="order-item" @click="goFinance">
								<view class="pic">
									<text class="iconfont icon-caiwuguanli"></text>
								</view>
								<view class="txt">财务管理</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 经营数据 - 参考会员中心的菜单样式 -->
				<view class="user-menus" style="margin-top: 20rpx;">
					<view class="menu-title">经营数据</view>
					<view class="data-grid">
						<view class="data-item">
							<view class="data-number">{{ overviewData.totalGoods || 0 }}</view>
							<view class="data-label">商品总数</view>
						</view>
						<view class="data-item">
							<view class="data-number">{{ overviewData.onSaleGoods || 0 }}</view>
							<view class="data-label">在售商品</view>
						</view>
						<view class="data-item">
							<view class="data-number">{{ overviewData.todayOrders || 0 }}</view>
							<view class="data-label">今日订单</view>
						</view>
						<view class="data-item">
							<view class="data-number">{{ overviewData.monthOrders || 0 }}</view>
							<view class="data-label">本月订单</view>
						</view>
					</view>
				</view>

				<!-- 最近订单 -->
				<view class="user-menus" style="margin-top: 20rpx;">
					<view class="menu-title">
						<text>最近订单</text>
						<text class="more-btn" @click="goOrderList">查看更多</text>
					</view>
					<view class="order-list">
						<view class="recent-order-item" v-for="order in recentOrders" :key="order.id" @click="goOrderDetail(order.id)">
							<view class="order-info">
								<view class="order-number">订单号：{{ order.order_sn }}</view>
								<view class="order-time">{{ order.created_at }}</view>
							</view>
							<view class="order-amount">¥{{ order.total_amount }}</view>
							<view class="order-status" :class="getOrderStatusClass(order.status)">
								{{ getOrderStatusText(order.status) }}
							</view>
						</view>
					</view>
				</view>

				<!-- 商家服务 -->
				<view class="user-menus" style="margin-top: 20rpx;">
					<view class="menu-title">商家服务</view>
					<view class="list-box">
						<view class="item" @click="goFinance">
							<image src="/static/images/menu.png"></image>
							<text>财务管理</text>
						</view>
						<view class="item" @click="goSettings">
							<image src="/static/images/menu.png"></image>
							<text>店铺设置</text>
						</view>
						<view class="item" @click="goStatistics">
							<image src="/static/images/menu.png"></image>
							<text>数据分析</text>
						</view>
						<view class="item" @click="backToUserCenter">
							<image src="/static/images/menu.png"></image>
							<text>返回会员中心</text>
						</view>
					</view>
				</view>

				<view class="uni-p-b-98"></view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { getMerchantInfo, getMerchantOverview } from '@/api/merchant_manage.js';
import { mapGetters } from 'vuex';
import colors from '@/mixins/color';

let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
const app = getApp();

export default {
	mixins: [colors],
	data() {
		return {
			merchantInfo: {},
			overviewData: {},
			recentOrders: [],
			sysHeight: sysHeight,
			// #ifdef H5 || MP
			pageHeight: '100%',
			// #endif
			// #ifdef APP-PLUS
			pageHeight: app.globalData.windowHeight,
			// #endif
		}
	},
	computed: {
		...mapGetters(['isLogin'])
	},
	onLoad() {
		if (!this.isLogin) {
			uni.navigateTo({
				url: '/pages/users/login/index'
			});
			return;
		}
		this.getMerchantData();
	},
	onShow() {
		this.getMerchantData();
		// 确保商家模式状态正确
		uni.setStorageSync('merchantMode', 'true');
	},
	methods: {
		// 获取商家数据
		async getMerchantData() {
			try {
				const [merchantRes, overviewRes] = await Promise.all([
					getMerchantInfo(),
					getMerchantOverview()
				]);
				console.log(merchantRes);
				this.merchantInfo = merchantRes.data;
				this.overviewData = overviewRes.data.overview;
				this.recentOrders = overviewRes.data.recentOrders || [];
			} catch (error) {
				this.$util.Tips({
					title: error || '获取数据失败'
				});
			}
		},

		// 跳转到商品列表
		goGoodsList() {
			uni.navigateTo({
				url: '/pages/merchant/goods/list'
			});
		},

		// 跳转到添加商品
		goAddGoods() {
			uni.navigateTo({
				url: '/pages/merchant/goods/add'
			});
		},

		// 跳转到订单列表
		goOrderList() {
			uni.navigateTo({
				url: '/pages/merchant/orders/list'
			});
		},

		// 跳转到数据统计
		goStatistics() {
			uni.navigateTo({
				url: '/pages/merchant/statistics/index'
			});
		},

		// 跳转到设置
		goSettings() {
			uni.navigateTo({
				url: '/pages/merchant/settings/index'
			});
		},

		// 跳转到财务管理
		goFinance() {
			uni.navigateTo({
				url: '/pages/merchant/finance/index'
			});
		},

		// 跳转到订单详情
		goOrderDetail(orderId) {
			uni.navigateTo({
				url: `/pages/merchant/orders/detail?id=${orderId}`
			});
		},

		// 返回会员中心
		backToUserCenter() {
			uni.switchTab({
				url: '/pages/user/index'
			});
		},

		// 切换到会员模式
		toggleMode() {
			// 设置为会员模式
			uni.setStorageSync('merchantMode', 'false');

			// 显示切换提示
			uni.showToast({
				title: '已切换到会员模式',
				icon: 'success',
				duration: 1500
			});

			// 跳转到会员中心
			setTimeout(() => {
				uni.switchTab({
					url: '/pages/user/index'
				});
			}, 1500);
		},

		// 获取订单状态文本
		getOrderStatusText(status) {
			const statusMap = {
				0: '待付款',
				1: '待发货',
				2: '待收货',
				3: '已完成',
				4: '已取消',
				5: '退款中'
			};
			return statusMap[status] || '未知状态';
		},

		// 获取订单状态样式类
		getOrderStatusClass(status) {
			const classMap = {
				0: 'status-pending',
				1: 'status-processing',
				2: 'status-shipping',
				3: 'status-completed',
				4: 'status-cancelled',
				5: 'status-refunding'
			};
			return classMap[status] || '';
		}
	}
}
</script>

<style lang="scss" scoped>
// 继承会员中心的样式
.new-users {
	display: flex;
	flex-direction: column;
	height: 100%;

	.sys-head {
		position: relative;
		width: 100%;

		.bg {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			background: var(--view-theme);
			background-size: 100% auto;
			background-position: left bottom;
		}

		.sys-title {
			z-index: 10;
			position: relative;
			height: 43px;
			text-align: center;
			line-height: 43px;
			font-size: 36rpx;
			color: #FFFFFF;
		}
	}

	.head {
		.user-card {
			position: relative;
			width: 100%;
			height: 380rpx;
			margin: 0 auto;
			padding: 35rpx 28rpx;
			background-image: url("~@/static/images/user01.png");
			background-size: 100% auto;
			background-color: var(--view-theme);
		}
	}
}

// 用户信息区域样式
.user-info {
	z-index: 20;
	position: relative;
	display: flex;

	.avatar-box {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
	}

	.avatar {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
	}

	.info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		margin-left: 20rpx;
		padding: 20rpx 0;

		.name {
			display: flex;
			align-items: center;
			color: #fff;
			font-size: 31rpx;

			.nickname {
				max-width: 8em;
			}
		}

		.num {
			display: flex;
			align-items: center;
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.6);

			.num-txt {
				height: 38rpx;
				background-color: rgba(255, 255, 255, 0.2);
				padding: 0 12rpx;
				border-radius: 16rpx;
				line-height: 38rpx;
				font-size: 24rpx;
			}
		}
	}

	.message {
		align-self: flex-start;
		position: relative;
		margin-top: 15rpx;
		margin-right: 20rpx;

		.iconfont {
			font-size: 40rpx;
			color: #fff;
		}
	}
}

// 数字显示区域
.num-wrapper {
	z-index: 30;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 22rpx;
	color: #fff;

	.num-item {
		width: 33.33%;
		text-align: center;

		&~.num-item {
			position: relative;

			&:before {
				content: '';
				position: absolute;
				width: 1rpx;
				height: 28rpx;
				top: 50%;
				margin-top: -14rpx;
				background-color: rgba(255, 255, 255, 0.4);
				left: 0;
			}
		}

		.num {
			font-size: 42rpx;
			font-weight: bold;
		}

		.txt {
			margin-top: 8rpx;
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.6);
		}
	}
}

// 状态文字样式
.status-text {
	color: white;

	&.active {
		background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
	}

	&.inactive {
		background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
	}
}

// 订单区域样式
.order-wrapper {
	background: #fff;
	margin: 0 30rpx;
	border-radius: 16rpx;
	position: relative;
	margin-top: -10rpx;

	.order-hd {
		justify-content: space-between;
		padding: 30rpx 20rpx 10rpx 30rpx;
		margin-top: 25rpx;
		font-size: 30rpx;
		color: #282828;

		.left {
			font-weight: bold;
		}
	}

	.order-bd {
		display: flex;
		padding: 0 0;

		.order-item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 20%;
			height: 140rpx;

			.pic {
				position: relative;
				text-align: center;

				.iconfont {
					font-size: 48rpx;
					color: var(--view-theme);
				}
			}

			.txt {
				margin-top: 6rpx;
				font-size: 26rpx;
				color: #333;
			}
		}
	}
}

// 菜单样式
.user-menus {
	background-color: #fff;
	margin: 0 30rpx;
	border-radius: 16rpx;

	.menu-title {
		padding: 30rpx 30rpx 40rpx;
		font-size: 30rpx;
		color: #282828;
		font-weight: bold;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.more-btn {
			font-size: 26rpx;
			color: #666666;
			font-weight: normal;
		}
	}

	.list-box {
		display: flex;
		flex-wrap: wrap;
		padding: 0;
	}

	.item {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-direction: column;
		width: 25%;
		margin-bottom: 47rpx;
		font-size: 26rpx;

		image {
			width: 58rpx;
			height: 48rpx;
		}

		text {
			margin-top: 6rpx;
			color: #333;
		}
	}
}

// 数据网格样式
.data-grid {
	display: flex;
	flex-wrap: wrap;
	padding: 0 30rpx 30rpx;
	gap: 20rpx;
	justify-content: space-between;

	// #ifdef APP-PLUS
	gap: 0;
	// #endif

	.data-item {
		width: calc(50% - 10rpx);
		min-width: calc(50% - 10rpx);
		max-width: calc(50% - 10rpx);
		text-align: center;
		padding: 20rpx;
		background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
		border-radius: 12rpx;
		border: 1rpx solid rgba(102, 126, 234, 0.1);
		box-sizing: border-box;
		transition: all 0.3s ease;
		flex: 0 0 calc(50% - 10rpx);

		// #ifdef APP-PLUS
		width: 48%;
		min-width: 48%;
		max-width: 48%;
		flex: 0 0 48%;
		margin-bottom: 20rpx;

		&:nth-child(odd) {
			margin-right: 4%;
		}
		// #endif

		&:active {
			transform: translateY(-4rpx);
			box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.15);
		}

		.data-number {
			font-size: 48rpx;
			font-weight: bold;
			color: var(--view-theme);
			margin-bottom: 8rpx;
		}

		.data-label {
			font-size: 26rpx;
			color: #666;
		}
	}
}

// 订单列表样式
.order-list {
	padding: 0 30rpx 30rpx;

	.recent-order-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}

		.order-info {
			flex: 1;

			.order-number {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 8rpx;
			}

			.order-time {
				font-size: 24rpx;
				color: #999;
			}
		}

		.order-amount {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-right: 20rpx;
		}

		.order-status {
			font-size: 24rpx;
			padding: 8rpx 16rpx;
			border-radius: 12rpx;
			color: white;

			&.status-pending {
				background: #FF9800;
			}

			&.status-processing {
				background: #2196F3;
			}

			&.status-shipping {
				background: #9C27B0;
			}

			&.status-completed {
				background: #4CAF50;
			}

			&.status-cancelled {
				background: #F44336;
			}

			&.status-refunding {
				background: #607D8B;
			}
		}
	}
}

// 模式切换按钮样式
.mode-switch {
	margin-left: 12rpx;
	display: flex;
	align-items: center;
	flex-shrink: 0;
}

.switch-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 8rpx 12rpx;
	border-radius: 16rpx;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	transition: all 0.3s ease;
	height: 60rpx;
	box-sizing: border-box;

	&:active {
		transform: scale(0.95);
	}

	&.merchant-mode {
		background: rgba(255, 255, 255, 0.3);
		border-color: rgba(255, 255, 255, 0.5);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}
}

.switch-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 6rpx;

	.iconfont {
		font-size: 24rpx;
		color: white;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
	}
}

.switch-text {
	font-size: 20rpx;
	color: white;
	font-weight: 500;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
	line-height: 1;
	white-space: nowrap;
}

// 通用样式
.uni-p-b-98 {
	padding-bottom: 98rpx;
}

// APP模式下强制2列布局
// #ifdef APP-PLUS
.data-grid {
	display: flex !important;
	flex-direction: row !important;
	flex-wrap: wrap !important;
	justify-content: space-between !important;

	.data-item {
		width: 48% !important;
		flex: 0 0 48% !important;
		display: flex !important;
		flex-direction: column !important;
		margin-bottom: 20rpx !important;

		&:nth-child(2n+1) {
			margin-right: 4% !important;
		}

		&:nth-child(2n) {
			margin-right: 0 !important;
		}
	}
}
// #endif


</style>
