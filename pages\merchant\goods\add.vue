<template>
	<view class="add-goods">
		<form @submit.prevent="submitForm">
			<!-- 商品图片 -->
			<view class="form-section">
				<view class="section-title">商品图片</view>
				<view class="image-upload">
					<view class="image-list">
						<view class="image-item" v-for="(image, index) in goodsForm.images" :key="index">
							<image :src="image" mode="aspectFill"></image>
							<view class="delete-btn" @click="removeImage(index)">
								<text class="iconfont icon-shanchu"></text>
							</view>
						</view>
						<view class="upload-btn" @click="uploadImage" v-if="goodsForm.images.length < 5">
							<text class="iconfont icon-tianjia"></text>
							<text>添加图片</text>
						</view>
					</view>
					<view class="upload-tip">最多可上传5张图片，第一张为主图</view>
				</view>
			</view>

			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<view class="form-item">
					<view class="label">商品名称</view>
					<view class="input-wrapper" @click="focusInput('name')">
						<input
							ref="nameInput"
							type="text"
							v-model="goodsForm.name"
							placeholder="请输入商品名称"
							maxlength="50"
							confirm-type="done"
							@input="onNameInput"
							@focus="onInputFocus"
							@blur="onInputBlur"
							:adjust-position="false"
							:hold-keyboard="false"
							:cursor-spacing="0"
						/>
					</view>
				</view>
				<view class="form-item">
					<view class="label">商品分类</view>
					<view class="picker-input" @click="showCategoryPicker">
						{{ selectedCategoryText || '请选择商品分类' }}
						<text class="iconfont icon-xiangyou"></text>
					</view>
				</view>
				<view class="form-item">
					<view class="label">商品描述</view>
					<textarea v-model="goodsForm.description" placeholder="请输入商品描述" maxlength="200"></textarea>
				</view>
			</view>

			<!-- 价格库存 -->
			<view class="form-section">
				<view class="section-title">价格库存</view>
				<view class="form-item">
					<view class="label">销售价格</view>
					<view class="input-wrapper" @click="focusInput('price')">
						<input
							ref="priceInput"
							type="number"
							v-model="goodsForm.price"
							placeholder="0.00"
							confirm-type="done"
							@input="onPriceInput"
							@focus="onInputFocus"
							@blur="onInputBlur"
							:adjust-position="false"
							:hold-keyboard="false"
							:cursor-spacing="0"
						/>
					</view>
				</view>
				<view class="form-item">
					<view class="label">原价</view>
					<view class="input-wrapper" @click="focusInput('originalPrice')">
						<input
							ref="originalPriceInput"
							type="number"
							v-model="goodsForm.original_price"
							placeholder="0.00（选填）"
							confirm-type="done"
							@input="onOriginalPriceInput"
							@focus="onInputFocus"
							@blur="onInputBlur"
							:adjust-position="false"
							:hold-keyboard="false"
							:cursor-spacing="0"
						/>
					</view>
				</view>
				<view class="form-item">
					<view class="label">库存数量</view>
					<view class="input-wrapper" @click="focusInput('stock')">
						<input
							ref="stockInput"
							type="number"
							v-model="goodsForm.stock"
							placeholder="请输入库存数量"
							confirm-type="done"
							@input="onStockInput"
							@focus="onInputFocus"
							@blur="onInputBlur"
							:adjust-position="false"
							:hold-keyboard="false"
							:cursor-spacing="0"
						/>
					</view>
				</view>
			</view>

			<!-- 商品详情 -->
			<view class="form-section">
				<view class="section-title">商品详情</view>
				<view class="detail-editor">
					<textarea v-model="goodsForm.detail" placeholder="请输入商品详细描述" maxlength="1000"></textarea>
				</view>
			</view>

			<!-- 其他设置 -->
			<view class="form-section">
				<view class="section-title">其他设置</view>
				<view class="form-item">
					<view class="label">商品状态</view>
					<switch :checked="goodsForm.status" @change="onStatusChange" />
					<text class="switch-text">{{ goodsForm.status ? '立即上架' : '暂不上架' }}</text>
				</view>
				<view class="form-item">
					<view class="label">商品重量</view>
					<view class="input-wrapper" @click="focusInput('weight')">
						<input
							ref="weightInput"
							type="number"
							v-model="goodsForm.weight"
							placeholder="0.00（kg）"
							confirm-type="done"
							@input="onWeightInput"
							@focus="onInputFocus"
							@blur="onInputBlur"
							:adjust-position="false"
							:hold-keyboard="false"
							:cursor-spacing="0"
						/>
					</view>
				</view>
			</view>



			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm" :disabled="submitting">
					{{ submitting ? '提交中...' : '保存商品' }}
				</button>
			</view>
		</form>

		<!-- 分类选择弹窗 -->
		<view class="category-picker-mask" v-if="showCategoryModal" @click="hideCategoryPicker">
			<view class="category-picker-content" @click.stop>
				<view class="category-picker-header">
					<text class="cancel-btn" @click="hideCategoryPicker">取消</text>
					<text class="title">选择分类</text>
					<text class="confirm-btn" @click="confirmCategorySelection">确定</text>
				</view>
				<view class="category-picker-body">
					<picker-view
						:key="pickerKey"
						:value="pickerValue"
						@change="onPickerViewChange"
						class="picker-view"
					>
						<picker-view-column>
							<view
								class="picker-item"
								v-for="(item, index) in categoryList"
								:key="item.id"
							>
								{{ item.name }}
							</view>
						</picker-view-column>
						<picker-view-column>
							<view
								class="picker-item"
								v-for="(item, index) in currentSecondCategories"
								:key="index"
							>
								{{ item.name }}
							</view>
						</picker-view-column>
					</picker-view>
				</view>
			</view>
		</view>

		<!-- 开发调试面板 -->
		<view class="debug-panel" v-if="isDevelopment">
			<view class="debug-header" @click="toggleDebugPanel">
				<text class="debug-title">🔧 调试面板</text>
				<text class="debug-toggle">{{ showDebugPanel ? '收起' : '展开' }}</text>
			</view>
			<view class="debug-content" v-if="showDebugPanel">
				<view class="debug-item">
					<text class="debug-label">分类数据源：</text>
					<text class="debug-value">{{ categoryList.length > 0 ? '已加载' : '未加载' }} ({{ categoryList.length }}个)</text>
				</view>
				<view class="debug-item">
					<text class="debug-label">当前二级分类：</text>
					<text class="debug-value">{{ currentSecondCategories.length }}个</text>
				</view>
				<view class="debug-item">
					<text class="debug-label">Picker值：</text>
					<text class="debug-value">[{{ pickerValue[0] }}, {{ pickerValue[1] }}]</text>
				</view>
				<view class="debug-item">
					<text class="debug-label">弹窗状态：</text>
					<text class="debug-value">{{ showCategoryModal ? '已打开' : '已关闭' }}</text>
				</view>
				<view class="debug-item">
					<text class="debug-label">当前选择：</text>
					<text class="debug-value">{{ goodsForm.category_name || '未选择' }}</text>
				</view>
				<view class="debug-item">
					<text class="debug-label">分类ID：</text>
					<text class="debug-value">{{ goodsForm.category_id || '无' }}</text>
				</view>
				<view class="debug-actions">
					<button class="debug-btn" @click="reloadCategories">重新加载分类</button>
					<button class="debug-btn" @click="clearSelection">清空选择</button>
					<button class="debug-btn" @click="forceLoadTestData">强制测试数据</button>
					<button class="debug-btn" @click="testPickerPosition">测试位置</button>
					<button class="debug-btn" @click="resetPickerPosition">重置位置</button>
					<button class="debug-btn" @click="testPickerData">测试数据</button>
					<button class="debug-btn" @click="logCategoryData">打印数据</button>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
import { addMerchantGoods, getGoodsCategories, uploadGoodsImage } from '@/api/merchant_manage.js';

export default {
	data() {
		return {
			goodsForm: {
				name: '',
				category_id: '',
				category_name: '',
				description: '',
				price: '',
				original_price: '',
				stock: '',
				detail: '',
				status: true,
				weight: '',
				images: []
			},
			categoryList: [],
			submitting: false,
			// 分类选择弹窗相关
			showCategoryModal: false,
			pickerValue: [0, 0],  // picker选中的索引值
			currentSecondCategories: [],  // 当前一级分类的二级分类
			isUpdatingData: false,  // 标记是否正在更新数据
			pickerKey: 0,  // picker-view的key，用于强制重新渲染
			// 开发调试相关
			isDevelopment: true,  // 设置为 false 可关闭测试数据
			showDebugPanel: false
		}
	},
	computed: {
		selectedCategoryText() {
			if (this.goodsForm.category_name) {
				return this.goodsForm.category_name;
			}
			return '';
		}
	},
	async onLoad() {
		console.log('页面加载开始');
		console.log('初始化商品表单数据:', this.goodsForm);

		try {
			// 先尝试加载接口数据
			await this.loadCategories();
			console.log('接口数据加载完成，分类数量:', this.categoryList.length);
		} catch (error) {
			console.log('接口数据加载失败:', error);
		}

		// 如果接口数据为空，添加测试数据
		if (this.categoryList.length === 0) {
			console.log('接口数据为空，加载测试数据');
			this.addTestData();
		}

		console.log('页面加载完成，最终分类数量:', this.categoryList.length);
	},
	methods: {
		// 添加测试数据
		addTestData() {
			// 创建测试分类数据
			const testCategories = [
				{
					id: 1,
					name: '服装鞋帽',
					children: [
						{ id: 11, name: '男装' },
						{ id: 12, name: '女装' },
						{ id: 13, name: '童装' },
						{ id: 14, name: '运动鞋' },
						{ id: 15, name: '皮鞋' }
					]
				},
				{
					id: 2,
					name: '数码电器',
					children: [
						{ id: 21, name: '手机' },
						{ id: 22, name: '电脑' },
						{ id: 23, name: '平板' },
						{ id: 24, name: '耳机' },
						{ id: 25, name: '音响' }
					]
				},
				{
					id: 3,
					name: '家居用品',
					children: [
						{ id: 31, name: '床上用品' },
						{ id: 32, name: '厨房用具' },
						{ id: 33, name: '收纳整理' },
						{ id: 34, name: '装饰摆件' }
					]
				},
				{
					id: 4,
					name: '美妆护肤',
					children: [
						{ id: 41, name: '面部护肤' },
						{ id: 42, name: '彩妆' },
						{ id: 43, name: '身体护理' },
						{ id: 44, name: '香水' }
					]
				},
				{
					id: 5,
					name: '食品饮料',
					children: [
						{ id: 51, name: '零食小食' },
						{ id: 52, name: '茶叶咖啡' },
						{ id: 53, name: '酒类' },
						{ id: 54, name: '保健品' }
					]
				},
				{
					id: 6,
					name: '图书文具',
					children: []  // 测试没有二级分类的情况
				},
				{
					id: 7,
					name: '运动户外',
					children: [
						{ id: 71, name: '健身器材' },
						{ id: 72, name: '户外装备' },
						{ id: 73, name: '球类运动' }
					]
				}
			];

			// 根据开发模式决定是否使用测试数据
			if (this.isDevelopment && this.categoryList.length === 0) {
				console.log('🔧 开发模式：开始加载测试数据');
				this.categoryList = testCategories;
				console.log('🔧 开发模式：测试数据设置完成:', this.categoryList);

				console.log('🔧 开发模式：测试数据加载完成');

				// 显示测试数据提示
				this.$util.Tips({
					title: '开发模式：已加载测试分类数据',
					icon: 'none'
				});
			}
		},

		// 加载商品分类
		async loadCategories() {
			try {
				const res = await getGoodsCategories();
				this.categoryList = res.data.list || [];
				console.log('从接口获取分类数据:', this.categoryList);
			} catch (error) {
				console.log('接口获取分类失败，将使用测试数据:', error);
				this.categoryList = [];
				// 错误提示改为警告，因为有测试数据兜底
				this.$util.Tips({
					title: '获取分类失败，使用测试数据'
				});
			}
		},

		// 显示分类选择弹窗
		showCategoryPicker() {
			this.showCategoryModal = true;
			// 使用nextTick确保弹窗完全显示后再初始化
			this.$nextTick(() => {
				this.initCategorySelection();
			});
		},

		// 隐藏分类选择弹窗
		hideCategoryPicker() {
			this.showCategoryModal = false;
		},

		// 初始化分类选择状态
		initCategorySelection() {
			if (this.categoryList.length === 0) return;

			console.log('初始化分类选择状态');

			// 重置选择状态
			this.pickerValue = [0, 0];

			// 初始化第二列数据（第一个一级分类的二级分类）
			this.updateSecondCategories(0);

			// 强制重新渲染picker-view
			this.pickerKey++;

			// 使用延迟确保picker-view完全渲染
			setTimeout(() => {
				// 如果有已选择的分类，恢复选择状态
				if (this.goodsForm.category_id) {
					this.restoreCategorySelection();
				} else {
					// 没有已选择的分类，确保显示在正确位置
					this.pickerValue = [0, 0];
					this.pickerKey++;
				}

				console.log('分类选择初始化完成:', {
					pickerValue: this.pickerValue,
					pickerKey: this.pickerKey,
					currentSecondCategories: this.currentSecondCategories.length
				});
			}, 100);
		},

		// 更新第二列分类数据
		updateSecondCategories(firstIndex) {
			console.log('更新第二列分类数据，firstIndex:', firstIndex);

			if (firstIndex >= 0 && firstIndex < this.categoryList.length) {
				const firstCategory = this.categoryList[firstIndex];
				console.log('选中的一级分类:', firstCategory);

				if (firstCategory.children && firstCategory.children.length > 0) {
					this.currentSecondCategories = [
						{ name: '请选择', id: '' },
						...firstCategory.children
					];
				} else {
					this.currentSecondCategories = [
						{ name: '无二级分类', id: '' }
					];
				}
			} else {
				this.currentSecondCategories = [];
			}

			console.log('更新后的二级分类:', this.currentSecondCategories);
		},

		// 恢复分类选择状态
		restoreCategorySelection() {
			// 查找当前选中的分类
			for (let i = 0; i < this.categoryList.length; i++) {
				const firstCategory = this.categoryList[i];

				// 检查是否是一级分类
				if (firstCategory.id == this.goodsForm.category_id) {
					this.pickerValue = [i, 0];
					this.updateSecondCategories(i);
					return;
				}

				// 检查二级分类
				if (firstCategory.children && firstCategory.children.length > 0) {
					const secondIndex = firstCategory.children.findIndex(child => child.id == this.goodsForm.category_id);
					if (secondIndex >= 0) {
						this.pickerValue = [i, secondIndex + 1]; // +1 因为第一个是"请选择"
						this.updateSecondCategories(i);
						return;
					}
				}
			}
		},

		// picker-view值改变事件
		onPickerViewChange(e) {
			// 如果正在更新数据，忽略这次change事件
			if (this.isUpdatingData) {
				console.log('正在更新数据，忽略change事件');
				return;
			}

			const values = e.detail.value;
			const firstIndex = values[0];
			const secondIndex = values[1];

			console.log('picker-view值改变:', {
				firstIndex,
				secondIndex,
				values,
				oldPickerValue: this.pickerValue,
				categoryName: this.categoryList[firstIndex]?.name
			});

			// 检查一级分类是否改变
			const firstCategoryChanged = firstIndex !== this.pickerValue[0];

			// 更新picker值
			this.pickerValue = values;

			// 如果一级分类改变了，更新二级分类列表
			if (firstCategoryChanged) {
				console.log('一级分类改变，从索引', this.pickerValue[0], '到', firstIndex);
				this.updateSecondCategoriesWithPosition(firstIndex, secondIndex);
			}

			console.log('picker-view值更新后:', this.pickerValue);
		},

		// 更新二级分类数据并保持位置
		updateSecondCategoriesWithPosition(firstIndex, secondIndex) {
			this.isUpdatingData = true;

			// 更新二级分类数据
			this.updateSecondCategories(firstIndex);

			// 强制重新渲染picker-view
			this.pickerKey++;

			// 使用nextTick确保DOM更新后再设置位置
			this.$nextTick(() => {
				// 检查secondIndex是否超出新的范围
				const maxSecondIndex = this.currentSecondCategories.length - 1;
				const validSecondIndex = secondIndex <= maxSecondIndex ? secondIndex : 0;

				// 重新设置picker值
				this.pickerValue = [firstIndex, validSecondIndex];

				console.log('位置调整:', {
					原始secondIndex: secondIndex,
					最大索引: maxSecondIndex,
					最终secondIndex: validSecondIndex,
					二级分类数量: this.currentSecondCategories.length,
					pickerKey: this.pickerKey
				});

				// 再次使用nextTick确保位置设置生效
				this.$nextTick(() => {
					this.isUpdatingData = false;
				});
			});
		},

		// 确认分类选择
		confirmCategorySelection() {
			const firstIndex = this.pickerValue[0];
			const secondIndex = this.pickerValue[1];

			if (firstIndex < 0 || firstIndex >= this.categoryList.length) {
				this.$util.Tips({ title: '请选择分类' });
				return;
			}

			const firstCategory = this.categoryList[firstIndex];
			const secondCategory = this.currentSecondCategories[secondIndex];

			let selectedCategory;
			let categoryName;

			// 判断选择的是一级分类还是二级分类
			if (!secondCategory || secondCategory.name === '请选择' || secondCategory.name === '无二级分类' || !secondCategory.id) {
				// 选择一级分类
				selectedCategory = firstCategory;
				categoryName = firstCategory.name;
			} else {
				// 选择二级分类
				selectedCategory = secondCategory;
				categoryName = `${firstCategory.name} > ${secondCategory.name}`;
			}

			// 更新表单数据
			this.goodsForm.category_id = selectedCategory.id;
			this.goodsForm.category_name = categoryName;

			console.log('确认选择的分类:', {
				id: selectedCategory.id,
				name: categoryName,
				category: selectedCategory
			});

			// 关闭弹窗
			this.hideCategoryPicker();
		},

		// 调试面板相关方法
		toggleDebugPanel() {
			this.showDebugPanel = !this.showDebugPanel;
		},

		// 重新加载分类数据
		async reloadCategories() {
			this.categoryList = [];
			this.currentSecondCategories = [];
			this.pickerValue = [0, 0];
			await this.loadCategories();
			this.addTestData();
			this.$util.Tips({
				title: '分类数据已重新加载',
				icon: 'success'
			});
		},

		// 清空分类选择
		clearSelection() {
			this.goodsForm.category_id = '';
			this.goodsForm.category_name = '';
			this.$util.Tips({
				title: '已清空分类选择',
				icon: 'success'
			});
		},

		// 强制加载测试数据
		forceLoadTestData() {
			console.log('🔧 开始强制加载测试数据');

			// 创建测试分类数据
			const testCategories = [
				{
					id: 1,
					name: '服装鞋帽',
					children: [
						{ id: 11, name: '男装' },
						{ id: 12, name: '女装' },
						{ id: 13, name: '童装' }
					]
				},
				{
					id: 2,
					name: '数码电器',
					children: [
						{ id: 21, name: '手机' },
						{ id: 22, name: '电脑' }
					]
				},
				{
					id: 3,
					name: '图书文具',
					children: []  // 测试没有二级分类的情况
				}
			];

			// 清空现有数据
			this.categoryList = [];
			this.currentSecondCategories = [];
			this.pickerValue = [0, 0];
			this.goodsForm.category_id = '';
			this.goodsForm.category_name = '';

			// 设置新数据
			this.categoryList = testCategories;

			console.log('🔧 强制加载测试数据完成:', {
				categoryList: this.categoryList
			});

			this.$util.Tips({
				title: '强制加载测试数据成功',
				icon: 'success'
			});
		},

		// 测试picker位置
		testPickerPosition() {
			console.log('🔧 测试picker位置');

			// 强制重置picker位置
			this.forceSetPickerPosition(1, 1);

			this.$util.Tips({
				title: '已设置picker位置为[1,1]',
				icon: 'success'
			});
		},

		// 重置picker位置到[0,0]
		resetPickerPosition() {
			console.log('🔧 重置picker位置到[0,0]');

			this.forceSetPickerPosition(0, 0);

			this.$util.Tips({
				title: '已重置picker位置为[0,0]',
				icon: 'success'
			});
		},

		// 测试picker数据
		testPickerData() {
			console.log('🔧 测试picker数据');
			console.log('categoryList:', this.categoryList);
			console.log('currentSecondCategories:', this.currentSecondCategories);
			console.log('pickerValue:', this.pickerValue);
			console.log('showCategoryModal:', this.showCategoryModal);

			// 显示第一个分类的名称
			if (this.categoryList.length > 0) {
				console.log('第一个分类名称:', this.categoryList[0].name);
				this.$util.Tips({
					title: `第一个分类: ${this.categoryList[0].name}`,
					icon: 'none'
				});
			} else {
				this.$util.Tips({
					title: '没有分类数据',
					icon: 'none'
				});
			}
		},

		// 强制设置picker位置
		forceSetPickerPosition(firstIndex, secondIndex) {
			console.log('强制设置picker位置:', [firstIndex, secondIndex]);

			this.isUpdatingData = true;

			// 先更新二级分类数据
			this.updateSecondCategories(firstIndex);

			// 强制重新渲染picker-view
			this.pickerKey++;

			// 使用多层nextTick确保位置设置正确
			this.$nextTick(() => {
				// 检查索引有效性
				const maxSecondIndex = this.currentSecondCategories.length - 1;
				const validSecondIndex = secondIndex <= maxSecondIndex ? secondIndex : 0;

				// 设置位置
				this.pickerValue = [firstIndex, validSecondIndex];

				console.log('位置设置完成:', {
					pickerValue: this.pickerValue,
					firstCategory: this.categoryList[firstIndex]?.name,
					secondCategory: this.currentSecondCategories[validSecondIndex]?.name,
					currentSecondCategories: this.currentSecondCategories,
					pickerKey: this.pickerKey
				});

				this.$nextTick(() => {
					this.isUpdatingData = false;
				});
			});
		},

		// 打印分类数据到控制台
		logCategoryData() {
			console.log('=== 分类数据调试信息 ===');
			console.log('分类列表:', this.categoryList);
			console.log('当前选择:', {
				category_id: this.goodsForm.category_id,
				category_name: this.goodsForm.category_name
			});
			console.log('Picker状态:', {
				pickerValue: this.pickerValue,
				currentSecondCategories: this.currentSecondCategories,
				showCategoryModal: this.showCategoryModal
			});
			this.$util.Tips({
				title: '数据已打印到控制台',
				icon: 'success'
			});
		},

		// 状态切换
		onStatusChange(e) {
			this.goodsForm.status = e.detail.value;
		},

		// 商品名称输入
		onNameInput(e) {
			this.goodsForm.name = e.detail.value;
		},

		// 销售价格输入
		onPriceInput(e) {
			let value = e.detail.value;
			// 只允许数字和小数点
			value = value.replace(/[^\d.]/g, '');
			// 确保只有一个小数点
			const parts = value.split('.');
			if (parts.length > 2) {
				value = parts[0] + '.' + parts.slice(1).join('');
			}
			// 限制小数位数为2位
			if (parts[1] && parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2);
			}
			// 限制整数部分不超过6位
			if (parts[0] && parts[0].length > 6) {
				value = parts[0].substring(0, 6) + (parts[1] ? '.' + parts[1] : '');
			}
			this.goodsForm.price = value;
		},

		// 原价输入
		onOriginalPriceInput(e) {
			let value = e.detail.value;
			// 只允许数字和小数点
			value = value.replace(/[^\d.]/g, '');
			// 确保只有一个小数点
			const parts = value.split('.');
			if (parts.length > 2) {
				value = parts[0] + '.' + parts.slice(1).join('');
			}
			// 限制小数位数为2位
			if (parts[1] && parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2);
			}
			// 限制整数部分不超过6位
			if (parts[0] && parts[0].length > 6) {
				value = parts[0].substring(0, 6) + (parts[1] ? '.' + parts[1] : '');
			}
			this.goodsForm.original_price = value;
		},

		// 库存数量输入
		onStockInput(e) {
			let value = e.detail.value;
			// 只允许数字
			value = value.replace(/[^\d]/g, '');
			// 限制不超过8位数
			if (value.length > 8) {
				value = value.substring(0, 8);
			}
			// 不允许以0开头（除非是单独的0）
			if (value.length > 1 && value.charAt(0) === '0') {
				value = value.substring(1);
			}
			this.goodsForm.stock = value;
		},

		// 商品重量输入
		onWeightInput(e) {
			let value = e.detail.value;
			// 只允许数字和小数点
			value = value.replace(/[^\d.]/g, '');
			// 确保只有一个小数点
			const parts = value.split('.');
			if (parts.length > 2) {
				value = parts[0] + '.' + parts.slice(1).join('');
			}
			// 限制小数位数为2位
			if (parts[1] && parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2);
			}
			// 限制整数部分不超过4位
			if (parts[0] && parts[0].length > 4) {
				value = parts[0].substring(0, 4) + (parts[1] ? '.' + parts[1] : '');
			}
			this.goodsForm.weight = value;
		},

		// 输入框获得焦点
		onInputFocus() {
			// 可以在这里添加获得焦点时的处理逻辑
		},

		// 输入框失去焦点
		onInputBlur() {
			// 可以在这里添加失去焦点时的处理逻辑
		},

		// 手动聚焦输入框
		focusInput(inputType) {
			this.$nextTick(() => {
				let inputRef;
				switch(inputType) {
					case 'name':
						inputRef = this.$refs.nameInput;
						break;
					case 'price':
						inputRef = this.$refs.priceInput;
						break;
					case 'originalPrice':
						inputRef = this.$refs.originalPriceInput;
						break;
					case 'stock':
						inputRef = this.$refs.stockInput;
						break;
					case 'weight':
						inputRef = this.$refs.weightInput;
						break;
				}

				if (inputRef) {
					try {
						inputRef.focus();
					} catch (error) {
						// 聚焦失败时静默处理
					}
				}
			});
		},



		// 上传图片
		uploadImage() {
			const that = this;
			uni.chooseImage({
				count: 5 - this.goodsForm.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					res.tempFilePaths.forEach(tempPath => {
						that.$util.uploadImgs('upload/image', tempPath, (uploadRes) => {
							if (uploadRes && uploadRes.data && uploadRes.data.url) {
								that.goodsForm.images.push(uploadRes.data.url);
							} else {
								that.$util.Tips({
									title: '图片上传失败，请重试'
								});
							}
						}, (error) => {
							that.$util.Tips({
								title: '图片上传失败，请重试'
							});
						});
					});
				},
				fail: (err) => {
					if (err.errMsg && !err.errMsg.includes('cancel')) {
						that.$util.Tips({
							title: '选择图片失败'
						});
					}
				}
			});
		},

		// 删除图片
		removeImage(index) {
			this.goodsForm.images.splice(index, 1);
		},

		// 表单验证
		validateForm() {
			if (!this.goodsForm.name.trim()) {
				this.$util.Tips({ title: '请输入商品名称' });
				return false;
			}
			if (!this.goodsForm.category_id) {
				this.$util.Tips({ title: '请选择商品分类' });
				return false;
			}
			if (!this.goodsForm.price || this.goodsForm.price <= 0) {
				this.$util.Tips({ title: '请输入正确的销售价格' });
				return false;
			}
			if (!this.goodsForm.stock || this.goodsForm.stock < 0) {
				this.$util.Tips({ title: '请输入正确的库存数量' });
				return false;
			}
			if (this.goodsForm.images.length === 0) {
				this.$util.Tips({ title: '请至少上传一张商品图片' });
				return false;
			}
			return true;
		},

		// 提交表单
		async submitForm() {
			if (!this.validateForm()) return;
			if (this.submitting) return;

			this.submitting = true;

			try {
				const formData = {
					...this.goodsForm,
					status: this.goodsForm.status ? 1 : 0,
					price: parseFloat(this.goodsForm.price),
					original_price: this.goodsForm.original_price ? parseFloat(this.goodsForm.original_price) : 0,
					stock: parseInt(this.goodsForm.stock),
					weight: this.goodsForm.weight ? parseFloat(this.goodsForm.weight) : 0
				};

				await addMerchantGoods(formData);

				this.$util.Tips({
					title: '商品添加成功',
					icon: 'success'
				});

				setTimeout(() => {
					uni.navigateBack();
				}, 1500);

			} catch (error) {
				this.$util.Tips({
					title: error.message || '添加失败，请重试'
				});
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.add-goods {
	background: linear-gradient(180deg, #f8f9ff 0%, #f5f5f5 100%);
	min-height: 100vh;
	padding-bottom: 140rpx;
}

.form-section {
	background: white;
	margin: 24rpx;
	padding: 40rpx 30rpx;
	border-radius: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: visible;
	z-index: 1;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
		z-index: 0;
	}
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 40rpx;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: -12rpx;
		left: 0;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
		border-radius: 2rpx;
	}
}

.image-upload {
	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;
	}

	.image-item {
		position: relative;
		width: 180rpx;
		height: 180rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.image-item image {
		width: 100%;
		height: 100%;
	}

	.delete-btn {
		position: absolute;
		top: 12rpx;
		right: 12rpx;
		width: 48rpx;
		height: 48rpx;
		background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
		transition: transform 0.3s ease;

		&:active {
			transform: scale(0.9);
		}
	}

	.delete-btn .iconfont {
		font-size: 28rpx;
		color: white;
	}

	.upload-btn {
		width: 180rpx;
		height: 180rpx;
		border: 3rpx dashed rgba(102, 126, 234, 0.3);
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: var(--view-theme);
		font-size: 26rpx;
		background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			border-color: var(--view-theme);
			background: rgba(102, 126, 234, 0.05);
		}
	}

	.upload-btn .iconfont {
		font-size: 56rpx;
		margin-bottom: 12rpx;
	}

	.upload-tip {
		font-size: 26rpx;
		color: #999;
		margin-top: 24rpx;
		padding: 16rpx 20rpx;
		background: rgba(102, 126, 234, 0.05);
		border-radius: 12rpx;
		border-left: 4rpx solid var(--view-theme);
	}
}

.form-item {
	display: flex;
	align-items: center;
	margin-bottom: 36rpx;
	position: relative;
	z-index: 1;
}

.form-item:last-child {
	margin-bottom: 0;
}

.label {
	width: 180rpx;
	font-size: 30rpx;
	color: #333;
	margin-right: 24rpx;
	font-weight: 500;
}

// 输入框包装器
.input-wrapper {
	flex: 1;
	position: relative;
	z-index: 10;
	min-height: 88rpx;
	cursor: text;

	// 添加点击提示
	&:active {
		opacity: 0.8;
	}

	input {
		width: 100% !important;
		height: 88rpx !important;
		min-height: 88rpx !important;
		padding: 24rpx 20rpx !important;
		border: 2rpx solid #e8eaed !important;
		border-radius: 12rpx !important;
		font-size: 30rpx !important;
		background: #fafbfc !important;
		transition: all 0.3s ease;
		box-sizing: border-box !important;
		outline: none !important;
		-webkit-appearance: none !important;
		appearance: none !important;
		color: #333 !important;
		line-height: 40rpx !important;
		position: relative;
		z-index: 10;
		display: block !important;

		// 确保在所有平台都能输入和点击
		-webkit-user-select: text !important;
		user-select: text !important;
		-webkit-touch-callout: default !important;
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
		pointer-events: auto !important;
		touch-action: manipulation !important;

		&:focus {
			border-color: var(--view-theme) !important;
			background: white !important;
			box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1) !important;
			outline: none !important;
			z-index: 20;
		}

		&:active {
			background: white !important;
			border-color: var(--view-theme) !important;
		}

		&::placeholder {
			color: #999 !important;
			font-size: 28rpx !important;
		}

		// H5端特殊处理
		// #ifdef H5
		&::-webkit-outer-spin-button,
		&::-webkit-inner-spin-button {
			-webkit-appearance: none !important;
			margin: 0 !important;
		}
		// #endif

		// APP端特殊处理
		// #ifdef APP-PLUS
		-webkit-user-select: text !important;
		user-select: text !important;
		-webkit-touch-callout: default !important;
		height: 88rpx !important;
		min-height: 88rpx !important;
		// #endif
	}
}

// 保留原有的textarea样式
.form-item textarea {
	flex: 1;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	background: #fafbfc;
	transition: all 0.3s ease;
	box-sizing: border-box;
	outline: none;
	color: #333;
	line-height: 1.4;

	&:focus {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
		outline: none;
	}

	&::placeholder {
		color: #999;
		font-size: 28rpx;
	}
}

.form-item textarea {
	height: 160rpx;
	resize: none;
}

.picker-input {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	color: #333;
	background: #fafbfc;
	transition: all 0.3s ease;

	&:active {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}
}

.picker-input .iconfont {
	color: var(--view-theme);
	font-size: 28rpx;
}

.switch-text {
	margin-left: 24rpx;
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.detail-editor textarea {
	width: 100%;
	height: 240rpx;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	box-sizing: border-box;
	background: #fafbfc;
	transition: all 0.3s ease;
	resize: none;

	&:focus {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	&::placeholder {
		color: #999;
		font-size: 28rpx;
	}
}

.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx;
	background: white;
	border-top: 1rpx solid #f0f0f0;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.submit-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	color: white;
	border: none;
	border-radius: 48rpx;
	font-size: 34rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
	}
}

.submit-btn:disabled {
	background: linear-gradient(135deg, #ccc 0%, #bbb 100%);
	box-shadow: none;
	transform: none;
}



/* 调试面板样式 */
.debug-panel {
	position: fixed;
	top: 100rpx;
	right: 20rpx;
	background: rgba(0, 0, 0, 0.8);
	border-radius: 12rpx;
	z-index: 999;
	min-width: 300rpx;
	max-width: 400rpx;
}

.debug-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.debug-title {
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
}

.debug-toggle {
	color: var(--view-theme);
	font-size: 26rpx;
}

.debug-content {
	padding: 20rpx;
}

.debug-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
	padding: 8rpx 0;
}

.debug-label {
	color: #ccc;
	font-size: 24rpx;
}

.debug-value {
	color: #fff;
	font-size: 24rpx;
	max-width: 200rpx;
	text-align: right;
	word-break: break-all;
}

.debug-actions {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
	margin-top: 20rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.debug-btn {
	flex: 1;
	min-width: 80rpx;
	height: 60rpx;
	background: var(--view-theme);
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 22rpx;
	line-height: 60rpx;
	text-align: center;
}

.debug-btn:active {
	opacity: 0.8;
}

/* 分类选择弹窗样式 */
.category-picker-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.category-picker-content {
	width: 100%;
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.category-picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
}

.category-picker-header .title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.category-picker-header .cancel-btn,
.category-picker-header .confirm-btn {
	font-size: 30rpx;
	color: var(--view-theme);
	padding: 10rpx 20rpx;
}

.category-picker-header .cancel-btn {
	color: #999;
}

.category-picker-body {
	flex: 1;
	padding: 20rpx 0;
}

.picker-view {
	width: 100%;
	height: 400rpx;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 30rpx;
	color: #333;
	line-height: 80rpx;
	box-sizing: border-box;
}

/* 修复picker-view选中项居中显示 */
.picker-view ::v-deep .uni-picker-view-indicator {
	height: 80rpx;
	border-top: 1rpx solid #e5e5e5;
	border-bottom: 1rpx solid #e5e5e5;
	background: rgba(0, 0, 0, 0.02);
}


</style>
