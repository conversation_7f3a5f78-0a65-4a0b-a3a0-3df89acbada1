<template>
	<view class="edit-goods">
		<form @submit="submitForm" v-if="!loading">
			<!-- 商品图片 -->
			<view class="form-section">
				<view class="section-title">商品图片</view>
				<view class="image-upload">
					<view class="image-list">
						<view class="image-item" v-for="(image, index) in goodsForm.images" :key="index">
							<image :src="image" mode="aspectFill"></image>
							<view class="delete-btn" @click="removeImage(index)">
								<text class="iconfont icon-shanchu"></text>
							</view>
						</view>
						<view class="upload-btn" @click="uploadImage" v-if="goodsForm.images.length < 5">
							<text class="iconfont icon-tianjia"></text>
							<text>添加图片</text>
						</view>
					</view>
					<view class="upload-tip">最多可上传5张图片，第一张为主图</view>
				</view>
			</view>

			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<view class="form-item">
					<view class="label">商品名称</view>
					<input type="text" v-model="goodsForm.name" placeholder="请输入商品名称" maxlength="50" />
				</view>
				<view class="form-item">
					<view class="label">一级分类</view>
					<picker @change="onFirstCategoryChange" :value="firstCategoryIndex" :range="categoryList" range-key="name">
						<view class="picker-input">
							{{ goodsForm.first_category_name || '请选择一级分类' }}
							<text class="iconfont icon-xiangyou"></text>
						</view>
					</picker>
				</view>
				<view class="form-item" v-if="secondCategoryList.length > 0">
					<view class="label">二级分类</view>
					<picker @change="onSecondCategoryChange" :value="secondCategoryIndex" :range="secondCategoryList" range-key="name">
						<view class="picker-input">
							{{ goodsForm.category_name || '请选择二级分类' }}
							<text class="iconfont icon-xiangyou"></text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<view class="label">商品描述</view>
					<textarea v-model="goodsForm.description" placeholder="请输入商品描述" maxlength="200"></textarea>
				</view>
			</view>

			<!-- 价格库存 -->
			<view class="form-section">
				<view class="section-title">价格库存</view>
				<view class="form-item">
					<view class="label">销售价格</view>
					<input type="digit" v-model="goodsForm.price" placeholder="0.00" />
				</view>
				<view class="form-item">
					<view class="label">原价</view>
					<input type="digit" v-model="goodsForm.original_price" placeholder="0.00（选填）" />
				</view>
				<view class="form-item">
					<view class="label">库存数量</view>
					<input type="number" v-model="goodsForm.stock" placeholder="请输入库存数量" />
				</view>
			</view>

			<!-- 商品详情 -->
			<view class="form-section">
				<view class="section-title">商品详情</view>
				<view class="detail-editor">
					<textarea v-model="goodsForm.detail" placeholder="请输入商品详细描述" maxlength="1000"></textarea>
				</view>
			</view>

			<!-- 其他设置 -->
			<view class="form-section">
				<view class="section-title">其他设置</view>
				<view class="form-item">
					<view class="label">商品状态</view>
					<switch v-model="goodsForm.status" :checked="goodsForm.status" />
					<text class="switch-text">{{ goodsForm.status ? '已上架' : '已下架' }}</text>
				</view>
				<view class="form-item">
					<view class="label">商品重量</view>
					<input type="digit" v-model="goodsForm.weight" placeholder="0.00（kg）" />
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" @click="submitForm" :disabled="submitting">
					{{ submitting ? '保存中...' : '保存修改' }}
				</button>
			</view>
		</form>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<text>加载中...</text>
		</view>
	</view>
</template>

<script>
import { getMerchantGoodsDetail, editMerchantGoods, getGoodsCategories } from '@/api/merchant_manage.js';

export default {
	data() {
		return {
			goodsId: '',
			goodsForm: {
				name: '',
				category_id: '',
				category_name: '',
				first_category_id: '',
				first_category_name: '',
				description: '',
				price: '',
				original_price: '',
				stock: '',
				detail: '',
				status: true,
				weight: '',
				images: []
			},
			categoryList: [],
			firstCategoryIndex: 0,
			secondCategoryList: [],
			secondCategoryIndex: 0,
			loading: true,
			submitting: false
		}
	},
	onLoad(options) {
		if (options.id) {
			this.goodsId = options.id;
			this.loadGoodsDetail();
			this.loadCategories();
		} else {
			uni.navigateBack();
		}
	},
	methods: {
		// 加载商品详情
		async loadGoodsDetail() {
			try {
				const res = await getMerchantGoodsDetail(this.goodsId);
				const goods = res.data;

				this.goodsForm = {
					name: goods.name,
					category_id: goods.category_id,
					category_name: goods.category_name,
					first_category_id: goods.first_category_id || '',
					first_category_name: goods.first_category_name || '',
					description: goods.description,
					price: goods.price.toString(),
					original_price: goods.original_price ? goods.original_price.toString() : '',
					stock: goods.stock.toString(),
					detail: goods.detail,
					status: goods.status == 1,
					weight: goods.weight ? goods.weight.toString() : '',
					images: goods.images || []
				};
			} catch (error) {
				this.$util.Tips({
					title: error.message || '获取商品详情失败'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} finally {
				this.loading = false;
			}
		},

		// 加载商品分类
		async loadCategories() {
			try {
				const res = await getGoodsCategories();
				this.categoryList = res.data || [];

				// 设置当前一级分类的索引
				let firstCategoryIndex = -1;
				let secondCategoryIndex = -1;

				// 查找当前商品的分类层级
				for (let i = 0; i < this.categoryList.length; i++) {
					const firstCategory = this.categoryList[i];

					// 检查是否是一级分类
					if (firstCategory.id == this.goodsForm.category_id) {
						firstCategoryIndex = i;
						this.goodsForm.first_category_id = firstCategory.id;
						this.goodsForm.first_category_name = firstCategory.name;
						this.secondCategoryList = [];
						break;
					}

					// 检查二级分类
					if (firstCategory.children && firstCategory.children.length > 0) {
						const secondIndex = firstCategory.children.findIndex(child => child.id == this.goodsForm.category_id);
						if (secondIndex >= 0) {
							firstCategoryIndex = i;
							secondCategoryIndex = secondIndex;
							this.goodsForm.first_category_id = firstCategory.id;
							this.goodsForm.first_category_name = firstCategory.name;
							this.secondCategoryList = firstCategory.children;
							break;
						}
					}
				}

				this.firstCategoryIndex = firstCategoryIndex >= 0 ? firstCategoryIndex : 0;
				this.secondCategoryIndex = secondCategoryIndex >= 0 ? secondCategoryIndex : 0;
			} catch (error) {
				console.log('获取分类失败:', error);
			}
		},

		// 一级分类选择
		onFirstCategoryChange(e) {
			const index = e.detail.value;
			this.firstCategoryIndex = index;
			const firstCategory = this.categoryList[index];
			this.goodsForm.first_category_id = firstCategory.id;
			this.goodsForm.first_category_name = firstCategory.name;

			// 更新二级分类列表
			this.secondCategoryList = firstCategory.children || [];
			this.secondCategoryIndex = 0;

			// 如果有二级分类，清空当前选择的二级分类
			if (this.secondCategoryList.length > 0) {
				this.goodsForm.category_id = '';
				this.goodsForm.category_name = '';
			} else {
				// 如果没有二级分类，直接使用一级分类
				this.goodsForm.category_id = firstCategory.id;
				this.goodsForm.category_name = firstCategory.name;
			}
		},

		// 二级分类选择
		onSecondCategoryChange(e) {
			const index = e.detail.value;
			this.secondCategoryIndex = index;
			const secondCategory = this.secondCategoryList[index];
			this.goodsForm.category_id = secondCategory.id;
			this.goodsForm.category_name = secondCategory.name;
		},

		// 上传图片
		uploadImage() {
			const that = this;
			uni.chooseImage({
				count: 5 - this.goodsForm.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					res.tempFilePaths.forEach(tempPath => {
						that.$util.uploadImgs('upload/image', tempPath, (uploadRes) => {
							if (uploadRes && uploadRes.data && uploadRes.data.url) {
								that.goodsForm.images.push(uploadRes.data.url);
							} else {
								that.$util.Tips({
									title: '图片上传失败，请重试'
								});
							}
						}, (error) => {
							that.$util.Tips({
								title: '图片上传失败，请重试'
							});
						});
					});
				},
				fail: (err) => {
					if (err.errMsg && !err.errMsg.includes('cancel')) {
						that.$util.Tips({
							title: '选择图片失败'
						});
					}
				}
			});
		},

		// 删除图片
		removeImage(index) {
			this.goodsForm.images.splice(index, 1);
		},

		// 表单验证
		validateForm() {
			if (!this.goodsForm.name.trim()) {
				this.$util.Tips({ title: '请输入商品名称' });
				return false;
			}
			if (!this.goodsForm.first_category_id) {
				this.$util.Tips({ title: '请选择一级分类' });
				return false;
			}
			// 如果有二级分类但未选择
			if (this.secondCategoryList.length > 0 && !this.goodsForm.category_id) {
				this.$util.Tips({ title: '请选择二级分类' });
				return false;
			}
			// 如果没有二级分类，确保category_id有值
			if (this.secondCategoryList.length === 0 && !this.goodsForm.category_id) {
				this.$util.Tips({ title: '请选择商品分类' });
				return false;
			}
			if (!this.goodsForm.price || this.goodsForm.price <= 0) {
				this.$util.Tips({ title: '请输入正确的销售价格' });
				return false;
			}
			if (!this.goodsForm.stock || this.goodsForm.stock < 0) {
				this.$util.Tips({ title: '请输入正确的库存数量' });
				return false;
			}
			if (this.goodsForm.images.length === 0) {
				this.$util.Tips({ title: '请至少上传一张商品图片' });
				return false;
			}
			return true;
		},

		// 提交表单
		async submitForm() {
			if (!this.validateForm()) return;
			if (this.submitting) return;

			this.submitting = true;

			try {
				const formData = {
					...this.goodsForm,
					status: this.goodsForm.status ? 1 : 0,
					price: parseFloat(this.goodsForm.price),
					original_price: this.goodsForm.original_price ? parseFloat(this.goodsForm.original_price) : 0,
					stock: parseInt(this.goodsForm.stock),
					weight: this.goodsForm.weight ? parseFloat(this.goodsForm.weight) : 0
				};

				await editMerchantGoods(this.goodsId, formData);

				this.$util.Tips({
					title: '商品修改成功',
					icon: 'success'
				});

				setTimeout(() => {
					uni.navigateBack();
				}, 1500);

			} catch (error) {
				this.$util.Tips({
					title: error.message || '修改失败，请重试'
				});
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.edit-goods {
	background: linear-gradient(180deg, #f8f9ff 0%, #f5f5f5 100%);
	min-height: 100vh;
	padding-bottom: 140rpx;
}

.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	color: #999;
	font-size: 30rpx;

	&::before {
		content: '';
		width: 80rpx;
		height: 80rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid var(--view-theme);
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.form-section {
	background: white;
	margin: 24rpx;
	padding: 40rpx 30rpx;
	border-radius: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
	}
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 40rpx;
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: -12rpx;
		left: 0;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(90deg, var(--view-theme) 0%, #667eea 100%);
		border-radius: 2rpx;
	}
}

.image-upload {
	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;
	}

	.image-item {
		position: relative;
		width: 180rpx;
		height: 180rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.image-item image {
		width: 100%;
		height: 100%;
	}

	.delete-btn {
		position: absolute;
		top: 12rpx;
		right: 12rpx;
		width: 48rpx;
		height: 48rpx;
		background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
		transition: transform 0.3s ease;

		&:active {
			transform: scale(0.9);
		}
	}

	.delete-btn .iconfont {
		font-size: 28rpx;
		color: white;
	}

	.upload-btn {
		width: 180rpx;
		height: 180rpx;
		border: 3rpx dashed rgba(102, 126, 234, 0.3);
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		color: var(--view-theme);
		font-size: 26rpx;
		background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			border-color: var(--view-theme);
			background: rgba(102, 126, 234, 0.05);
		}
	}

	.upload-btn .iconfont {
		font-size: 56rpx;
		margin-bottom: 12rpx;
	}

	.upload-tip {
		font-size: 26rpx;
		color: #999;
		margin-top: 24rpx;
		padding: 16rpx 20rpx;
		background: rgba(102, 126, 234, 0.05);
		border-radius: 12rpx;
		border-left: 4rpx solid var(--view-theme);
	}
}

.form-item {
	display: flex;
	align-items: center;
	margin-bottom: 36rpx;
	position: relative;
}

.form-item:last-child {
	margin-bottom: 0;
}

.label {
	width: 180rpx;
	font-size: 30rpx;
	color: #333;
	margin-right: 24rpx;
	font-weight: 500;
}

.form-item input,
.form-item textarea {
	flex: 1;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	background: #fafbfc;
	transition: all 0.3s ease;

	&:focus {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	&::placeholder {
		color: #999;
		font-size: 28rpx;
	}
}

.form-item textarea {
	height: 160rpx;
	resize: none;
}

.picker-input {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	color: #333;
	background: #fafbfc;
	transition: all 0.3s ease;

	&:active {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}
}

.picker-input .iconfont {
	color: var(--view-theme);
	font-size: 28rpx;
}

.switch-text {
	margin-left: 24rpx;
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.detail-editor textarea {
	width: 100%;
	height: 240rpx;
	padding: 24rpx 20rpx;
	border: 2rpx solid #e8eaed;
	border-radius: 12rpx;
	font-size: 30rpx;
	box-sizing: border-box;
	background: #fafbfc;
	transition: all 0.3s ease;
	resize: none;

	&:focus {
		border-color: var(--view-theme);
		background: white;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	&::placeholder {
		color: #999;
		font-size: 28rpx;
	}
}

.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30rpx;
	background: white;
	border-top: 1rpx solid #f0f0f0;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.submit-btn {
	width: 100%;
	height: 96rpx;
	background: linear-gradient(135deg, var(--view-theme) 0%, #667eea 100%);
	color: white;
	border: none;
	border-radius: 48rpx;
	font-size: 34rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
	}
}

.submit-btn:disabled {
	background: linear-gradient(135deg, #ccc 0%, #bbb 100%);
	box-shadow: none;
	transform: none;
}
</style>
