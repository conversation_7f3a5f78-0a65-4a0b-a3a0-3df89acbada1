<template>
	<view class="shop-detail-container" :style="colorStyle">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight }">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<text class="iconfont icon-fanhui"></text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">{{ shopInfo.name || '店铺详情' }}</text>
				</view>
				<view class="navbar-right">
					<text class="iconfont" :class="isGridLayout ? 'icon-pailie' : 'icon-tupianpailie'" @click="toggleLayout"></text>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input">
				<text class="iconfont icon-sousuo"></text>
				<input type="text" v-model="searchKeyword" placeholder="搜索店内商品" @confirm="searchGoods" />
			</view>
		</view>

		<!-- 店铺信息头部 -->
		<view class="shop-header" style="margin-top: 232rpx;">
			<view class="shop-info">
				<view class="shop-logo">
					<image :src="shopInfo.logo || defaultLogo" mode="aspectFill"></image>
				</view>
				<view class="shop-details">
					<view class="shop-name">{{ shopInfo.name || '店铺名称' }}</view>
					<view class="shop-desc">{{ shopInfo.description || '暂无描述' }}</view>
					<view class="shop-stats">
						<view class="stat-item">
							<text class="stat-value">{{ shopInfo.rating || '5.0' }}</text>
							<text class="stat-label">评分</text>
						</view>
						<view class="stat-item">
							<text class="stat-value">{{ shopInfo.sales || 0 }}</text>
							<text class="stat-label">销量</text>
						</view>
						<view class="stat-item">
							<text class="stat-value">{{ shopInfo.goods_count || 0 }}</text>
							<text class="stat-label">商品</text>
						</view>
					</view>
				</view>
				<view class="shop-follow" @click="toggleFollow">
					<text class="follow-btn" :class="{ followed: shopInfo.is_followed }">
						{{ shopInfo.is_followed ? '已关注' : '关注' }}
					</text>
				</view>
			</view>
		</view>

		<!-- 商品分类筛选 -->
		<view class="goods-filter">
			<scroll-view scroll-x="true" class="filter-scroll">
				<view class="filter-list">
					<view 
						class="filter-item" 
						:class="{ active: currentFilter === item.value }" 
						v-for="item in filterList" 
						:key="item.value"
						@click="setFilter(item.value)"
					>
						{{ item.label }}
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 商品列表 -->
		<scroll-view
			class="goods-scroll"
			:style="{ height: getGoodsScrollHeight() }"
			scroll-y="true"
			@scrolltolower="loadMore"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>
			<!-- 网格布局 -->
			<view class="goods-list grid-layout" v-if="isGridLayout">
				<view class="goods-item" v-for="goods in goodsList" :key="goods.id" @click="goGoodsDetail(goods)">
					<view class="goods-image">
						<image :src="goods.image" mode="aspectFill"></image>
						<view class="goods-tag" v-if="goods.activity">
							<text v-if="goods.activity.type === '1'">秒杀</text>
							<text v-else-if="goods.activity.type === '2'">砍价</text>
							<text v-else-if="goods.activity.type === '3'">拼团</text>
						</view>
					</view>
					<view class="goods-info">
						<view class="goods-name">{{ goods.store_name }}</view>
						<view class="goods-price">
							<text class="price-symbol">¥</text>
							<text class="price-value">{{ goods.price }}</text>
							<text class="price-original" v-if="goods.ot_price && goods.ot_price > goods.price">¥{{ goods.ot_price }}</text>
						</view>
						<view class="goods-sales">已售 {{ goods.sales || 0 }}{{ goods.unit_name || '件' }}</view>
					</view>
				</view>
			</view>

			<!-- 列表布局 -->
			<view class="goods-list list-layout" v-else>
				<view class="goods-item-list" v-for="goods in goodsList" :key="goods.id" @click="goGoodsDetail(goods)">
					<view class="goods-image-list">
						<image :src="goods.image" mode="aspectFill"></image>
						<view class="goods-tag" v-if="goods.activity">
							<text v-if="goods.activity.type === '1'">秒杀</text>
							<text v-else-if="goods.activity.type === '2'">砍价</text>
							<text v-else-if="goods.activity.type === '3'">拼团</text>
						</view>
					</view>
					<view class="goods-info-list">
						<view class="goods-name-list">{{ goods.store_name }}</view>
						<view class="goods-price-list">
							<text class="price-symbol">¥</text>
							<text class="price-value">{{ goods.price }}</text>
							<text class="price-original" v-if="goods.ot_price && goods.ot_price > goods.price">¥{{ goods.ot_price }}</text>
						</view>
						<view class="goods-sales-list">已售 {{ goods.sales || 0 }}{{ goods.unit_name || '件' }}</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="goodsList.length > 0">
				<text v-if="loading">加载中...</text>
				<text v-else-if="!hasMore">没有更多商品了</text>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="!loading && goodsList.length === 0">
				<image src="/static/images/def_avatar.png" mode="aspectFit"></image>
				<text>暂无商品</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { getShopDetail, getShopGoods, followShop } from '@/api/shop.js';
import colors from "@/mixins/color";
import { goShopDetail } from '@/libs/order.js';

export default {
	mixins: [colors],
	data() {
		return {
			statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 'px',
			shopId: '',
			searchKeyword: '',
			currentFilter: 'all',
			isGridLayout: true, // 是否网格布局
			filterList: [
				{ label: '全部', value: 'all' },
				{ label: '热销', value: 'hot' },
				{ label: '新品', value: 'new' },
				{ label: '价格', value: 'price' }
			],
			shopInfo: {},
			goodsList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			limit: 20,
			defaultLogo: '/static/images/def_avatar.png'
		};
	},
	onLoad(options) {
		this.shopId = options.id;
		if (this.shopId) {
			this.loadShopDetail();
			this.loadGoodsList();
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 设置筛选条件
		setFilter(value) {
			this.currentFilter = value;
			this.refreshGoodsList();
		},

		// 切换布局
		toggleLayout() {
			this.isGridLayout = !this.isGridLayout;
		},

		// 计算商品列表的高度
		getGoodsScrollHeight() {
			const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
			const navbarHeight = 88; // rpx
			const searchBarHeight = 112; // rpx (搜索栏一直显示)
			const shopHeaderHeight = 200; // rpx (店铺信息头部大概高度)
			const filterBarHeight = 72; // rpx (筛选栏高度)

			// 将px转换为rpx (1px = 2rpx on most devices)
			const statusBarHeightRpx = statusBarHeight * 2;
			const totalTopHeight = statusBarHeightRpx + navbarHeight + searchBarHeight + shopHeaderHeight + filterBarHeight;

			return `calc(100vh - ${totalTopHeight}rpx)`;
		},

		// 搜索商品
		searchGoods() {
			this.refreshGoodsList();
		},

		// 加载店铺详情
		async loadShopDetail() {
			try {
				const res = await getShopDetail(this.shopId);
				this.shopInfo = res.data || {};
			} catch (error) {
				// 使用模拟数据
				this.shopInfo = this.getMockShopData();
			}
		},

		// 加载商品列表
		async loadGoodsList(refresh = false) {
			if (this.loading) return;
			
			this.loading = true;
			
			if (refresh) {
				this.page = 1;
				this.goodsList = [];
				this.hasMore = true;
			}

			try {
				const params = {
					page: this.page,
					limit: this.limit,
					sort: this.currentFilter,
					keyword: this.searchKeyword
				};

				const res = await getShopGoods(this.shopId, params);
				const newList = res.data.list || [];
				
				if (refresh) {
					this.goodsList = newList;
				} else {
					this.goodsList.push(...newList);
				}
				
				this.hasMore = newList.length >= this.limit;
				this.page++;
			} catch (error) {
				// 使用模拟数据
				const mockData = this.getMockGoodsData();
				if (refresh) {
					this.goodsList = mockData;
				} else {
					this.goodsList.push(...mockData);
				}
				this.hasMore = false;
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 刷新商品列表
		refreshGoodsList() {
			this.loadGoodsList(true);
		},

		// 下拉刷新
		onRefresh() {
			this.refreshing = true;
			this.loadShopDetail();
			this.refreshGoodsList();
		},

		// 加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.loadGoodsList();
			}
		},

		// 跳转商品详情
		goGoodsDetail(goods) {
			goShopDetail(goods, this.$store.getters.uid).then(() => {
				uni.navigateTo({
					url: `/pages/goods_details/index?id=${goods.id}`
				});
			});
		},

		// 关注/取消关注
		async toggleFollow() {
			try {
				await followShop(this.shopId);
				this.shopInfo.is_followed = !this.shopInfo.is_followed;
				this.$util.Tips({
					title: this.shopInfo.is_followed ? '关注成功' : '取消关注成功'
				});
			} catch (error) {
				this.$util.Tips({
					title: error.message || '操作失败'
				});
			}
		},

		// 获取模拟店铺数据
		getMockShopData() {
			return {
				id: this.shopId,
				name: '熊猫百货',
				logo: '/static/images/def_avatar.png',
				description: '优质商品，品质保证',
				rating: '4.8',
				sales: 1234,
				goods_count: 156,
				is_followed: false
			};
		},

		// 获取模拟商品数据
		getMockGoodsData() {
			return [
				{
					id: 1,
					store_name: '好大夫在线开方',
					image: '/static/images/1-001.png',
					price: '99.00',
					ot_price: '129.00',
					sales: 234,
					unit_name: '件',
					activity: { type: '1' }
				},
				{
					id: 2,
					store_name: '力度伸护手霜',
					image: '/static/images/1-002.png',
					price: '134.00',
					sales: 156,
					unit_name: '件'
				},
				{
					id: 3,
					store_name: '时尚女包',
					image: '/static/images/2-001.png',
					price: '639.00',
					ot_price: '799.00',
					sales: 89,
					unit_name: '件',
					activity: { type: '2' }
				},
				{
					id: 4,
					store_name: '精美项链',
					image: '/static/images/2-002.png',
					price: '69.00',
					sales: 345,
					unit_name: '件'
				}
			];
		}
	}
}
</script>

<style lang="scss" scoped>
.shop-detail-container {
	min-height: 100vh;
	background: #F5F5F5;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);

	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;

		.navbar-left {
			width: 80rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.iconfont {
				font-size: 36rpx;
				color: #FFFFFF;
			}
		}

		.navbar-right {
			width: 120rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: flex-end;

			.iconfont {
				font-size: 36rpx;
				color: #FFFFFF;
			}
		}

		.navbar-center {
			flex: 1;
			text-align: center;

			.navbar-title {
				font-size: 36rpx;
				font-weight: 600;
				color: #FFFFFF;
			}
		}
	}
}

/* 搜索栏 */
.search-bar {
	position: fixed;
	top: 88rpx;
	left: 0;
	right: 0;
	z-index: 998;
	background: #FFFFFF;
	padding: 20rpx 32rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

	.search-input {
		flex: 1;
		height: 72rpx;
		background: #F8F9FA;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 24rpx;

		.iconfont {
			font-size: 32rpx;
			color: #CCCCCC;
			margin-right: 16rpx;
		}

		input {
			flex: 1;
			font-size: 28rpx;
			color: #333333;
		}
	}
}

/* 店铺信息头部 */
.shop-header {
	background: #FFFFFF;
	padding: 32rpx;
	margin-bottom: 20rpx;

	.shop-info {
		display: flex;
		align-items: flex-start;
		flex-direction: row;

		.shop-logo {
			width: 120rpx;
			height: 120rpx;
			border-radius: 16rpx;
			overflow: hidden;
			margin-right: 24rpx;
			flex-shrink: 0;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.shop-details {
			flex: 1;
			margin-right: 20rpx;

			.shop-name {
				font-size: 36rpx;
				font-weight: 600;
				color: #333333;
				margin-bottom: 8rpx;
			}

			.shop-desc {
				font-size: 28rpx;
				color: #666666;
				margin-bottom: 20rpx;
			}

			.shop-stats {
				display: flex;
				align-items: center;

				.stat-item {
					margin-right: 40rpx;
					text-align: center;

					.stat-value {
						display: block;
						font-size: 32rpx;
						font-weight: 600;
						color: #333333;
						margin-bottom: 4rpx;
					}

					.stat-label {
						font-size: 24rpx;
						color: #999999;
					}
				}
			}
		}

		.shop-follow {
			.follow-btn {
				padding: 16rpx 32rpx;
				border: 2rpx solid #FF6B35;
				border-radius: 32rpx;
				font-size: 28rpx;
				color: #FF6B35;
				background: transparent;

				&.followed {
					background: #FF6B35;
					color: #FFFFFF;
				}
			}
		}
	}
}

/* 商品分类筛选 */
.goods-filter {
	background: #FFFFFF;
	border-bottom: 1rpx solid #F0F0F0;

	.filter-scroll {
		white-space: nowrap;

		.filter-list {
			display: flex;
			padding: 0 32rpx;

			.filter-item {
				flex-shrink: 0;
				padding: 24rpx 32rpx;
				font-size: 28rpx;
				color: #666666;
				position: relative;

				&.active {
					color: #FF6B35;
					font-weight: 600;

					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 40rpx;
						height: 4rpx;
						background: #FF6B35;
						border-radius: 2rpx;
					}
				}
			}
		}
	}
}

/* 商品列表滚动区域 */
.goods-scroll {
	background: transparent;
}

/* 商品列表 */
.goods-list {
	padding: 20rpx;

	/* 网格布局 */
	&.grid-layout {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		.goods-item {
			width: 345rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

			.goods-image {
				position: relative;
				width: 100%;
				height: 345rpx;

				image {
					width: 100%;
					height: 100%;
				}

				.goods-tag {
					position: absolute;
					top: 16rpx;
					left: 16rpx;
					padding: 8rpx 16rpx;
					background: #FF6B35;
					border-radius: 16rpx;
					font-size: 20rpx;
					color: #FFFFFF;
				}
			}

			.goods-info {
				padding: 20rpx 17rpx 26rpx 17rpx;

				.goods-name {
					font-size: 30rpx;
					color: #333333;
					line-height: 42rpx;
					height: 84rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					margin-bottom: 8rpx;
				}

				.goods-price {
					display: flex;
					align-items: baseline;
					margin-bottom: 8rpx;

					.price-symbol {
						font-size: 24rpx;
						color: #FF6B35;
						font-weight: 600;
					}

					.price-value {
						font-size: 32rpx;
						color: #FF6B35;
						font-weight: 600;
						margin-right: 16rpx;
					}

					.price-original {
						font-size: 24rpx;
						color: #999999;
						text-decoration: line-through;
					}
				}

				.goods-sales {
					font-size: 24rpx;
					color: #999999;
				}
			}
		}
	}

	/* 列表布局 */
	&.list-layout {
		.goods-item-list {
			background: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			padding: 30rpx;
			display: flex;
			align-items: flex-start;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

			.goods-image-list {
				position: relative;
				width: 180rpx;
				height: 180rpx;
				margin-right: 24rpx;
				flex-shrink: 0;

				image {
					width: 100%;
					height: 100%;
					border-radius: 16rpx;
				}

				.goods-tag {
					position: absolute;
					top: 8rpx;
					left: 8rpx;
					padding: 6rpx 12rpx;
					background: #FF6B35;
					border-radius: 12rpx;
					font-size: 18rpx;
					color: #FFFFFF;
				}
			}

			.goods-info-list {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				height: 180rpx;

				.goods-name-list {
					font-size: 30rpx;
					color: #333333;
					line-height: 42rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					margin-bottom: 16rpx;
				}

				.goods-price-list {
					display: flex;
					align-items: baseline;
					margin-bottom: 16rpx;

					.price-symbol {
						font-size: 24rpx;
						color: #FF6B35;
						font-weight: 600;
					}

					.price-value {
						font-size: 32rpx;
						color: #FF6B35;
						font-weight: 600;
						margin-right: 16rpx;
					}

					.price-original {
						font-size: 24rpx;
						color: #999999;
						text-decoration: line-through;
					}
				}

				.goods-sales-list {
					font-size: 24rpx;
					color: #999999;
				}
			}
		}
	}
}

/* 加载更多 */
.load-more {
	text-align: center;
	padding: 40rpx 0;
	font-size: 28rpx;
	color: #999999;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;

	image {
		width: 300rpx;
		height: 240rpx;
		margin-bottom: 32rpx;
	}

	text {
		font-size: 28rpx;
		color: #999999;
	}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.shop-info {
		flex-direction: column;
		align-items: flex-start !important;

		.shop-logo {
			margin-bottom: 16rpx;
		}

		.shop-details {
			margin-right: 0 !important;
			margin-bottom: 16rpx;
		}
	}

	.goods-list {
		&.grid-layout {
			.goods-item {
				width: calc(50% - 10rpx);

				.goods-image {
					height: 280rpx !important;
				}
			}
		}

		&.list-layout {
			.goods-item-list {
				.goods-image-list {
					width: 140rpx;
					height: 140rpx;
				}

				.goods-info-list {
					height: 140rpx;

					.goods-name-list {
						font-size: 28rpx;
					}

					.goods-price-list {
						.price-value {
							font-size: 28rpx;
						}
					}
				}
			}
		}
	}
}
</style>
